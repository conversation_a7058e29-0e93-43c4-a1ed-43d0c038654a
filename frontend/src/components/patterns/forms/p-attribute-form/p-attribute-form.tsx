import { Component, Element, Event, EventEmitter, Listen, State, h } from '@stencil/core';
import { FrontendLogger } from '../../../../global/script/var';

interface FeatureItem {
  label: string;
  value: string;
}

@Component({
  tag: 'p-feature-form',
  styleUrl: '../p-priority-item-form/p-priority-item-form.css',
  shadow: true,
})
export class PFeatureForm {
  @Element() el: HTMLElement;

  @Event({
    eventName: 'addCustomSenseChoiceFeature',
    bubbles: true,
  })
  addCustomSenseChoiceFeatureEventEmitter: EventEmitter;

  @Event({
    eventName: 'modalCloseEvent',
    bubbles: true,
  })
  modalCloseEventEmitter: EventEmitter;

  @State() featureLabel: string = '';
  @State() formErrors: { [key: string]: string } = {};

  componentDidLoad() {
    // Focus on the label input when modal opens
    const labelInput = this.el.shadowRoot?.querySelector(
      'e-input[name="featureLabel"]',
    ) as HTMLElement;
    if (labelInput) {
      setTimeout(() => labelInput.focus(), 100);
    }
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    const { name, value } = event.detail;

    if (name === 'featureLabel') {
      this.featureLabel = value;
      // Clear label error when user starts typing
      if (this.formErrors.label) {
        this.formErrors = { ...this.formErrors };
        delete this.formErrors.label;
      }
    }
  }

  @Listen('buttonClickEvent')
  handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'saveFeature') {
      this.saveFeature();
    } else if (event.detail.action === 'cancelFeature') {
      this.closeModal();
    }
  }

  private validateForm(): boolean {
    const errors: { [key: string]: string } = {};

    if (!this.featureLabel.trim()) {
      errors.label = 'Feature name is required';
    }

    this.formErrors = errors;
    return Object.keys(errors).length === 0;
  }

  private saveFeature() {
    if (!this.validateForm()) {
      return;
    }

    // Generate value from label
    const featureValue = this.featureLabel
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    const feature: FeatureItem = {
      label: this.featureLabel.trim(),
      value: featureValue,
    };

    FrontendLogger.debug('Emitting addCustomSenseChoiceFeature with:', feature);

    this.addCustomSenseChoiceFeatureEventEmitter.emit({
      feature: feature,
    });
  }

  private closeModal() {
    this.modalCloseEventEmitter.emit();
  }

  render() {
    return (
      <div class="priority-item-form">
        <e-text>
          <strong>
            Feature name <span class="mandatory"> * </span>
          </strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-input
          type="text"
          name="featureLabel"
          placeholder="e.g. Price, Quality, Design"
          value={this.featureLabel}
        ></e-input>
        <l-spacer value={0.5}></l-spacer>
        <e-text variant="footnote">
          Keep the feature name short and clear. Examples: Price, Quality, Speed, Design, Features
        </e-text>
        {this.formErrors.label && (
          <div class="error-message">
            <e-text variant="footnote">{this.formErrors.label}</e-text>
          </div>
        )}
        <l-spacer value={3}></l-spacer>
        <l-row justifyContent="space-between">
          <e-button variant="light" action="cancelFeature">
            Cancel
          </e-button>
          <e-button action="saveFeature" disabled={!this.featureLabel.trim()}>
            Add
          </e-button>
        </l-row>
      </div>
    );
  }
}
