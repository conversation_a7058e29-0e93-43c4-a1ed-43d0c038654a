import { FrontendLogger } from '../../../../../../global/script/var';
import { ApiWrapper } from '../../../../../../global/script/helpers/api/ApiWrapper';

export interface ConjointProfilesPayload {
  attributes: { label: string; value: string }[];
  attributeLevels: { [key: string]: { label: string; value: string }[] };
  productContext?: {
    industry?: string;
    productType?: string;
    description?: string;
  };
  maxProfiles?: number;
}

/**
 * Generate conjoint analysis profiles using AI
 *
 * This API calls the backend AI service to generate realistic product combinations
 * for conjoint analysis instead of using cartesian product which creates many nonsensical combinations.
 *
 * @param payload - Conjoint profile generation parameters
 * @returns Promise with generated conjoint profiles
 */
export const generateConjointProfilesApi = async (payload: ConjointProfilesPayload) => {
  try {
    FrontendLogger.debug('Generating conjoint profiles with AI', {
      attributeCount: payload.attributes.length,
      maxProfiles: payload.maxProfiles || 20,
      hasProductContext: !!payload.productContext,
    });

    const result = await <PERSON><PERSON><PERSON>rapper(`/ai/generate-conjoint-profiles`, {
      method: 'POST',
      body: payload,
      includeCsrf: true,
    });

    FrontendLogger.debug('Conjoint profiles generation completed', {
      success: result.success,
      profileCount: result.payload?.profiles?.length || 0,
      aiGenerated: result.payload?.metadata?.aiGenerated,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    FrontendLogger.error('Error generating conjoint profiles', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      attributeCount: payload.attributes.length,
    });

    return {
      success: false,
      message: 'Error generating conjoint profiles. Please try again.',
      payload: null,
    };
  }
};
