export interface ConjointConcept {
  conceptId: string;
  features: { [key: string]: string }; // featureValue -> selected variant value
  selected?: boolean;
}

export interface ConjointChoiceTask {
  taskId: string;
  taskNumber: number;
  alternatives: ConjointConcept[];
  includeNoneOption: boolean;
  selected?: boolean;
}

export interface senseChoiceConfigInterface {
  type: string; // 'lite' or 'full'
  features: { label: string; value: string }[];
  featureVariants: { [key: string]: { label: string; value: string }[] }; // featureValue -> variants array
  selectedConcepts: ConjointConcept[]; // User-curated concepts
  choiceTasks: ConjointChoiceTask[]; // User-curated tasks
  thankYouMessage: string;
}
